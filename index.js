const express = require('express');
const cors = require('cors');

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.MailUtil = require('./lib/util/mail');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

// Middleware
const bodyParser = require('body-parser');
const tokenToUserMiddleware = require('./lib/middleware/tokenToUser');
const verifyTokenMiddleware = require('./lib/middleware/verifyToken');
const verifyPermission = require('./lib/middleware/verifyPermission');

// Socket.IO
const socketManager = require('./lib/socket/socketManager');

// Handle routes
const UserHandle = require('./lib/routes/user');
const UserAdminHandle = require('./lib/routes/admin/user');
const PermissionAdminHandle = require('./lib/routes/admin/permission');
const GroupPermissionAdminHandle = require('./lib/routes/admin/groupPermission');
const CategoryPermissionAdminHandle = require('./lib/routes/admin/categoryPermission');
const UnitAdminHandle = require('./lib/routes/admin/unit');
const PositionAdminHandle = require('./lib/routes/admin/position');
const AreaAdminHandle = require('./lib/routes/admin/area');
const JobTypeAdminHandle = require('./lib/routes/admin/jobType');
const DutyShiftTemplateHandle = require('./lib/routes/dutyShiftTemplate');
const DutySpecializedScheduleHandle = require('./lib/routes/dutySpecializedSchedule');
const DutyShiftHandle = require('./lib/routes/dutyShift');
const DutyCriminalScheduleHandle = require('./lib/routes/dutyCriminalSchedule');
const DutyMainScheduleHandle = require('./lib/routes/dutyMainSchedule');
const DutySubScheduleHandle = require('./lib/routes/dutySubSchedule');
const ReportHandle = require('./lib/routes/report');
const DutyLocationScheduleHandle = require('./lib/routes/dutyLocationSchedule');
const DutyPatrolScheduleHandle = require('./lib/routes/dutyPatrolSchedule');
const DutyStadiumScheduleHandle = require('./lib/routes/dutyStadiumSchedule');
const DutyEmergencyScheduleHandle = require('./lib/routes/dutyEmergencySchedule');

// Attendance system handles
const WorkScheduleHandle = require('./lib/routes/workSchedule');
const WorkScheduleAdminHandle = require('./lib/routes/admin/workSchedule');
const AttendanceHandle = require('./lib/routes/attendance');
const AttendanceAdminHandle = require('./lib/routes/admin/attendance');
const LeaveRequestHandle = require('./lib/routes/leaveRequest');

// Statistics system handles
const StatisticsHandle = require('./lib/routes/statistics');

// Start server
const app = express();
app.set('trust proxy', true);
const server = require('http').Server(app);

// Initialize Socket.IO with our custom manager
socketManager.initialize(server);
global.io = socketManager.io; // For backward compatibility

// Middleware setup
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));
app.use(express.static('public'));

// Define route declaration function
const declareRoute = (method, routeName, middlewares = [], destinationRoute) => {
  if (!destinationRoute || !routeName) {
    return;
  }

  Object.keys(destinationRoute).forEach((version) => {
    app[method](`/api/${version}${routeName}`, middlewares, destinationRoute[version]);
  });
};

// API Routes - Example routes for the template
declareRoute('post', '/user/login', [], UserHandle.login);
declareRoute('post', '/user/logout', [tokenToUserMiddleware], UserHandle.logout);
declareRoute('post', '/user/get', [tokenToUserMiddleware], UserHandle.get);
declareRoute('post', '/user/change-password', [tokenToUserMiddleware], UserHandle.changePassword);
declareRoute('post', '/user/send-otp', [], UserHandle.sendOTP);
declareRoute('post', '/user/change-password-otp', [], UserHandle.checkOTPChangePassword);

declareRoute('post', '/admin/user/list', [tokenToUserMiddleware, verifyPermission('xem-danh-sach-tai-khoan')], UserAdminHandle.list);
declareRoute('post', '/admin/user/create', [tokenToUserMiddleware, verifyPermission('create-user')], UserAdminHandle.create);
declareRoute('post', '/admin/user/update', [tokenToUserMiddleware, verifyPermission('update-user')], UserAdminHandle.update);
declareRoute('post', '/admin/user/inactive', [tokenToUserMiddleware, verifyPermission('inactive-user')], UserAdminHandle.inactive);
declareRoute('post', '/admin/user/get', [tokenToUserMiddleware, verifyPermission('get-user')], UserAdminHandle.get);
declareRoute('post', '/admin/user/grant-permission', [tokenToUserMiddleware, verifyPermission('grant-pemission')], UserAdminHandle.grantPermission);
declareRoute('post', '/admin/user/toggle-block', [tokenToUserMiddleware, verifyPermission('update-user')], UserAdminHandle.toggleBlock);

declareRoute('post', '/admin/permission/list', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], PermissionAdminHandle.list);
declareRoute('post', '/admin/permission/create', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], PermissionAdminHandle.create);
declareRoute('post', '/admin/permission/update', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], PermissionAdminHandle.update);
declareRoute('post', '/admin/permission/inactive', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], PermissionAdminHandle.inactive);
declareRoute('post', '/admin/permission/get', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], PermissionAdminHandle.get);

declareRoute('post', '/admin/group-permission/list', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], GroupPermissionAdminHandle.list);
declareRoute('post', '/admin/group-permission/create', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], GroupPermissionAdminHandle.create);
declareRoute('post', '/admin/group-permission/update', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], GroupPermissionAdminHandle.update);
declareRoute('post', '/admin/group-permission/inactive', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], GroupPermissionAdminHandle.inactive);
declareRoute('post', '/admin/group-permission/get', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], GroupPermissionAdminHandle.get);

declareRoute('post', '/admin/unit/list', [tokenToUserMiddleware, verifyPermission('xem-danh-sach-don-vi')], UnitAdminHandle.list);
declareRoute('post', '/admin/unit/get', [tokenToUserMiddleware, verifyPermission('xem-danh-sach-don-vi')], UnitAdminHandle.get);
declareRoute('post', '/admin/unit/list-level', [tokenToUserMiddleware, verifyPermission('xem-danh-sach-don-vi')], UnitAdminHandle.listUnitLevel);
declareRoute('post', '/admin/unit/create', [tokenToUserMiddleware, verifyPermission('taodonvi')], UnitAdminHandle.create);
declareRoute('post', '/admin/unit/update', [tokenToUserMiddleware, verifyPermission('sua-don-vi')], UnitAdminHandle.update);
declareRoute('post', '/admin/unit/inactive', [tokenToUserMiddleware, verifyPermission('xoa-don-vi')], UnitAdminHandle.inactive);
declareRoute('post', '/admin/unit/ordering', [tokenToUserMiddleware, verifyPermission('sua-don-vi')], UnitAdminHandle.ordering);

declareRoute('post', '/admin/position/list', [tokenToUserMiddleware, verifyPermission('xem-danh-sach-don-vi')], PositionAdminHandle.list);
declareRoute('post', '/admin/position/get', [tokenToUserMiddleware, verifyPermission('xem-danh-sach-don-vi')], PositionAdminHandle.get);
declareRoute('post', '/admin/position/create', [tokenToUserMiddleware, verifyPermission('taodonvi')], PositionAdminHandle.create);
declareRoute('post', '/admin/position/update', [tokenToUserMiddleware, verifyPermission('sua-don-vi')], PositionAdminHandle.update);
declareRoute('post', '/admin/position/inactive', [tokenToUserMiddleware, verifyPermission('xoa-don-vi')], PositionAdminHandle.inactive);
declareRoute('post', '/admin/position/ordering', [tokenToUserMiddleware, verifyPermission('sua-don-vi')], PositionAdminHandle.ordering);

declareRoute('post', '/admin/category-permission/list', [tokenToUserMiddleware], CategoryPermissionAdminHandle.list);

declareRoute('post', '/admin/area/list', [tokenToUserMiddleware, verifyPermission('quan-ly-khu-vuc')], AreaAdminHandle.list);
declareRoute('post', '/admin/area/get', [tokenToUserMiddleware, verifyPermission('quan-ly-khu-vuc')], AreaAdminHandle.get);
declareRoute('post', '/admin/area/create', [tokenToUserMiddleware, verifyPermission('quan-ly-khu-vuc')], AreaAdminHandle.create);
declareRoute('post', '/admin/area/update', [tokenToUserMiddleware, verifyPermission('quan-ly-khu-vuc')], AreaAdminHandle.update);
declareRoute('post', '/admin/area/inactive', [tokenToUserMiddleware, verifyPermission('quan-ly-khu-vuc')], AreaAdminHandle.inactive);

declareRoute('post', '/admin/job-type/list', [tokenToUserMiddleware, verifyPermission('quan-ly-loai-cong-viec')], JobTypeAdminHandle.list);
declareRoute('post', '/admin/job-type/get', [tokenToUserMiddleware, verifyPermission('quan-ly-loai-cong-viec')], JobTypeAdminHandle.get);
declareRoute('post', '/admin/job-type/create', [tokenToUserMiddleware, verifyPermission('quan-ly-loai-cong-viec')], JobTypeAdminHandle.create);
declareRoute('post', '/admin/job-type/update', [tokenToUserMiddleware, verifyPermission('quan-ly-loai-cong-viec')], JobTypeAdminHandle.update);
declareRoute('post', '/admin/job-type/inactive', [tokenToUserMiddleware, verifyPermission('quan-ly-loai-cong-viec')], JobTypeAdminHandle.inactive);


// Duty Shift Template Routes
declareRoute('post', '/duty-shift-template/create', [tokenToUserMiddleware, verifyPermission('create-duty-shift-template')], DutyShiftTemplateHandle.create);
declareRoute('post', '/duty-shift-template/update', [tokenToUserMiddleware, verifyPermission('update-duty-shift-template')], DutyShiftTemplateHandle.update);
declareRoute('post', '/duty-shift-template/inactive', [tokenToUserMiddleware, verifyPermission('inactive-duty-shift-template')], DutyShiftTemplateHandle.inactive);

// Duty Specialized Schedule Routes
declareRoute('post', '/duty-specialized-schedule/get', [tokenToUserMiddleware, verifyPermission('get-duty-specialized-schedule')], DutySpecializedScheduleHandle.get);
declareRoute('post', '/duty-specialized-schedule/update-shift', [tokenToUserMiddleware, verifyPermission('update-shift-duty-specialized-schedule')], DutySpecializedScheduleHandle.updateShift);
declareRoute('post', '/duty-specialized-schedule/inactive', [tokenToUserMiddleware, verifyPermission('inactive-duty-specialized-schedule')], DutySpecializedScheduleHandle.inactive);

// Duty Shift Routes
declareRoute('post', '/duty-shift/create', [tokenToUserMiddleware, verifyPermission('create-duty-shift')], DutyShiftHandle.create);
declareRoute('post', '/duty-shift/update', [tokenToUserMiddleware, verifyPermission('update-duty-shift')], DutyShiftHandle.update);
declareRoute('post', '/duty-shift/inactive', [tokenToUserMiddleware, verifyPermission('inactive-duty-shift')], DutyShiftHandle.inactive);
declareRoute('post', '/duty-shift/list', [tokenToUserMiddleware, verifyPermission('list-duty-shift')], DutyShiftHandle.list);
declareRoute('post', '/duty-shift/get', [tokenToUserMiddleware, verifyPermission('get-duty-shift')], DutyShiftHandle.get);

// Duty Criminal Schedule Routes
declareRoute('post', '/duty-criminal-schedule/get', [tokenToUserMiddleware, verifyPermission('get-duty-criminal-schedule')], DutyCriminalScheduleHandle.get);
declareRoute('post', '/duty-criminal-schedule/update-shift', [tokenToUserMiddleware, verifyPermission('update-shift-duty-criminal-schedule')], DutyCriminalScheduleHandle.updateShift);
declareRoute('post', '/duty-criminal-schedule/update-template', [tokenToUserMiddleware, verifyPermission('update-template-duty-criminal-schedule')], DutyCriminalScheduleHandle.updateTemplate);
declareRoute('post', '/duty-criminal-schedule/inactive', [tokenToUserMiddleware, verifyPermission('inactive-duty-criminal-schedule')], DutyCriminalScheduleHandle.inactive);

// Duty Main Schedule Routes
declareRoute('post', '/duty-main-schedule/get', [tokenToUserMiddleware, verifyPermission('get-duty-main-schedule')], DutyMainScheduleHandle.get);
declareRoute('post', '/duty-main-schedule/update-shift', [tokenToUserMiddleware, verifyPermission('update-shift-duty-main-schedule')], DutyMainScheduleHandle.updateShift);
declareRoute('post', '/duty-main-schedule/inactive', [tokenToUserMiddleware, verifyPermission('inactive-duty-main-schedule')], DutyMainScheduleHandle.inactive);

// Duty Sub Schedule Routes
declareRoute('post', '/duty-sub-schedule/get', [tokenToUserMiddleware, verifyPermission('get-duty-sub-schedule')], DutySubScheduleHandle.get);
declareRoute('post', '/duty-sub-schedule/update-shift', [tokenToUserMiddleware, verifyPermission('update-shift-duty-sub-schedule')], DutySubScheduleHandle.updateShift);
declareRoute('post', '/duty-sub-schedule/inactive', [tokenToUserMiddleware, verifyPermission('inactive-duty-sub-schedule')], DutySubScheduleHandle.inactive);

// Report Routes
declareRoute('post', '/report/create', [tokenToUserMiddleware], ReportHandle.create);
declareRoute('post', '/report/list', [tokenToUserMiddleware], ReportHandle.list);
declareRoute('post', '/report/statistics', [tokenToUserMiddleware], ReportHandle.statistics);
declareRoute('post', '/report/templates', [tokenToUserMiddleware], ReportHandle.templates);

// Duty Location Schedule Routes
declareRoute('post', '/duty-location-schedule/get', [tokenToUserMiddleware, verifyPermission('get-duty-location-schedule')], DutyLocationScheduleHandle.get);
declareRoute('post', '/duty-location-schedule/update-shift', [tokenToUserMiddleware, verifyPermission('update-shift-duty-location-schedule')], DutyLocationScheduleHandle.updateShift);
declareRoute('post', '/duty-location-schedule/inactive', [tokenToUserMiddleware, verifyPermission('inactive-duty-location-schedule')], DutyLocationScheduleHandle.inactive);
declareRoute('post', '/duty-location-schedule/update-template', [tokenToUserMiddleware, verifyPermission('update-template-duty-location-schedule')], DutyLocationScheduleHandle.updateTemplate);
declareRoute('post', '/duty-location-schedule/list-location', [tokenToUserMiddleware, verifyPermission('list-duty-location')], DutyLocationScheduleHandle.listLocation);

// Duty Patrol Schedule Routes
declareRoute('post', '/duty-patrol-schedule/get', [tokenToUserMiddleware, verifyPermission('get-duty-patrol-schedule')], DutyPatrolScheduleHandle.get);
declareRoute('post', '/duty-patrol-schedule/update-shift', [tokenToUserMiddleware, verifyPermission('update-shift-duty-patrol-schedule')], DutyPatrolScheduleHandle.updateShift);
declareRoute('post', '/duty-patrol-schedule/inactive', [tokenToUserMiddleware, verifyPermission('inactive-duty-patrol-schedule')], DutyPatrolScheduleHandle.inactive);
declareRoute('post', '/duty-patrol-schedule/update-template', [tokenToUserMiddleware, verifyPermission('update-template-duty-patrol-schedule')], DutyPatrolScheduleHandle.updateTemplate);
// Duty Stadium Schedule Routes
declareRoute('post', '/duty-stadium-schedule/get', [tokenToUserMiddleware, verifyPermission('get-duty-stadium-schedule')], DutyStadiumScheduleHandle.get);
declareRoute('post', '/duty-stadium-schedule/update-shift', [tokenToUserMiddleware, verifyPermission('update-shift-duty-stadium-schedule')], DutyStadiumScheduleHandle.updateShift);
declareRoute('post', '/duty-stadium-schedule/inactive', [tokenToUserMiddleware, verifyPermission('inactive-duty-stadium-schedule')], DutyStadiumScheduleHandle.inactive);
declareRoute('post', '/duty-stadium-schedule/update-template', [tokenToUserMiddleware, verifyPermission('update-template-duty-stadium-schedule')], DutyStadiumScheduleHandle.updateTemplate);

// Duty Emergency Schedule Routes
declareRoute('post', '/duty-emergency-schedule/get', [tokenToUserMiddleware, verifyPermission('get-duty-emergency-schedule')], DutyEmergencyScheduleHandle.get);
declareRoute('post', '/duty-emergency-schedule/update-shift', [tokenToUserMiddleware, verifyPermission('update-shift-duty-emergency-schedule')], DutyEmergencyScheduleHandle.updateShift);
declareRoute('post', '/duty-emergency-schedule/inactive', [tokenToUserMiddleware, verifyPermission('inactive-duty-emergency-schedule')], DutyEmergencyScheduleHandle.inactive);
declareRoute('post', '/duty-emergency-schedule/update-template', [tokenToUserMiddleware, verifyPermission('update-template-duty-emergency-schedule')], DutyEmergencyScheduleHandle.updateTemplate);

// Work Schedule Routes
declareRoute('post', '/admin/work-schedule/create', [tokenToUserMiddleware, verifyPermission('tao-lich-lam-viec')], WorkScheduleAdminHandle.create);
declareRoute('post', '/admin/work-schedule/list', [tokenToUserMiddleware, verifyPermission('tao-lich-lam-viec')], WorkScheduleAdminHandle.list);
declareRoute('post', '/admin/work-schedule/update', [tokenToUserMiddleware, verifyPermission('tao-lich-lam-viec')], WorkScheduleAdminHandle.update);
declareRoute('post', '/admin/work-schedule/delete', [tokenToUserMiddleware, verifyPermission('tao-lich-lam-viec')], WorkScheduleAdminHandle.delete);
declareRoute('post', '/work-schedule/get', [tokenToUserMiddleware], WorkScheduleHandle.get);

// Attendance Routes
declareRoute('post', '/attendance/checkin', [tokenToUserMiddleware], AttendanceHandle.checkin);
declareRoute('post', '/attendance/history', [tokenToUserMiddleware], AttendanceHandle.history);
declareRoute('post', '/attendance/status', [tokenToUserMiddleware], AttendanceHandle.status);
declareRoute('post', '/attendance/statistics', [tokenToUserMiddleware], AttendanceHandle.statistics);
declareRoute('post', '/admin/attendance/statistics', [tokenToUserMiddleware, verifyPermission('xem-thong-ke-diem-danh')], AttendanceAdminHandle.statistics);
declareRoute('post', '/admin/attendance/daily-statistics', [tokenToUserMiddleware, verifyPermission('xem-thong-ke-diem-danh')], AttendanceAdminHandle['daily-statistics']);

// Leave Request Routes
declareRoute('post', '/leave-request/create', [tokenToUserMiddleware], LeaveRequestHandle.create);
declareRoute('post', '/leave-request/list', [tokenToUserMiddleware], LeaveRequestHandle.list);
declareRoute('post', '/leave-request/detail', [tokenToUserMiddleware], LeaveRequestHandle.detail);
declareRoute('post', '/admin/leave-request/approve', [tokenToUserMiddleware, verifyPermission('duyet-don-xin-nghi')], LeaveRequestHandle.approve);

// Statistics Routes
declareRoute('post', '/statistics/on-duty-officers', [tokenToUserMiddleware], StatisticsHandle.onDutyOfficers);
declareRoute('post', '/statistics/late-attendance', [tokenToUserMiddleware], StatisticsHandle.lateAttendance);
declareRoute('post', '/statistics/officer-summary', [tokenToUserMiddleware], StatisticsHandle.officerSummary);
declareRoute('post', '/statistics/officers-by-area', [tokenToUserMiddleware], StatisticsHandle.officersByArea);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date() });
});

// // Start attendance notification job
// const attendanceNotificationJob = require('./lib/jobs/attendanceNotificationJob');
// attendanceNotificationJob.start(1); // Run every minute

// // Start weekly schedule job
// const weeklyScheduleJob = require('./lib/jobs/weeklyScheduleJob');
// weeklyScheduleJob.start(); // Run at 22:00 every Friday

// Start statistics metadata job
const statisticsMetadataJob = require('./lib/jobs/statisticsMetadataJob');
statisticsMetadataJob.start(); // Run metadata calculation every 10 minutes, trigger processing every minute

// Start attendance status sync job
const AttendanceStatusSyncJobStartup = require('./lib/startup/attendanceStatusSyncJobStartup');
AttendanceStatusSyncJobStartup.init(); // Run at 12:00 and 18:00 daily

const port = _.get(config, 'port', 3000);
server.listen(port, () => {
  logger.logInfo('Server listening at port:', port);
});

process.on('uncaughtException', (err) => {
  logger.logError('uncaughtException', err);
});
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully...');
  AttendanceStatusSyncJobStartup.shutdown();
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully...');
  AttendanceStatusSyncJobStartup.shutdown();
  process.exit(0);
});
