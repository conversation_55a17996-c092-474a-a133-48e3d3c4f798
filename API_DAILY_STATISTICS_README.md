# API Thống Kê Điểm Danh Theo <PERSON>

## Tổng quan
API mới được tạo để thống kê điểm danh theo ngày, cung cấp dữ liệu chi tiết về tình hình điểm danh của cán bộ được nhóm theo từng ngày trong khoảng thời gian chỉ định.

## Endpoint
```
POST /api/v1.0/admin/attendance/daily-statistics
```

## Phân quyền
- Yêu cầu token hợp lệ
- Cần có quyền `xem-thong-ke-diem-danh`

## Tham số đầu vào

### Body Parameters (JSON)
| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `startDate` | String | Có | <PERSON><PERSON><PERSON> bắt đầ<PERSON> (định dạng DD-MM-YYYY) |
| `endDate` | String | Có | Ng<PERSON>y kết thúc (định dạng DD-MM-YYYY) |
| `unitId` | String | Không | ID đơn vị để filter (ObjectId) |

### Validation Rules
- `startDate` và `endDate` phải có định dạng DD-MM-YYYY
- `startDate` phải nhỏ hơn hoặc bằng `endDate`
- Khoảng thời gian không được vượt quá 31 ngày
- `unitId` phải là ObjectId hợp lệ (nếu có)

## Dữ liệu trả về

### Thành công (200)
```json
{
  "code": 200,
  "message": {
    "head": "Thành công",
    "body": "Lấy thống kê điểm danh theo ngày thành công"
  },
  "data": {
    "period": {
      "startDate": "15-08-2024",
      "endDate": "19-08-2024"
    },
    "scope": "all",
    "dailyStatistics": [
      {
        "date": "15-08-2024",
        "summary": {
          "totalScheduled": 100,
          "totalAttended": 85,
          "onTime": 70,
          "late": 15,
          "absent": 15,
          "attendanceRate": 85
        },
        "byUnit": [
          {
            "unitId": "60f1b2b3c4d5e6f7a8b9c0d1",
            "unitName": "Phòng Cảnh sát Giao thông",
            "totalScheduled": 20,
            "totalAttended": 18,
            "onTime": 15,
            "late": 3,
            "absent": 2,
            "attendanceRate": 90
          }
        ]
      }
    ]
  }
}
```

### Lỗi (400/403/500)
```json
{
  "code": 400,
  "message": {
    "head": "Lỗi tham số",
    "body": "Bạn vui lòng kiểm tra lại dữ liệu vừa nhập. Xin cảm ơn."
  }
}
```

## Cấu trúc dữ liệu chi tiết

### Summary (Thống kê tổng theo ngày)
- `totalScheduled`: Tổng số ca làm việc được lên lịch
- `totalAttended`: Tổng số ca đã điểm danh
- `onTime`: Số ca điểm danh đúng giờ
- `late`: Số ca điểm danh muộn
- `absent`: Số ca vắng mặt (có lịch nhưng không điểm danh)
- `attendanceRate`: Tỷ lệ điểm danh (%)

### ByUnit (Thống kê theo đơn vị)
- `unitId`: ID của đơn vị
- `unitName`: Tên đơn vị
- Các trường thống kê tương tự như Summary

## Ví dụ sử dụng

### 1. Thống kê 7 ngày gần đây
```bash
curl -X POST http://localhost:9654/api/v1.0/admin/attendance/daily-statistics \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "startDate": "13-08-2024",
    "endDate": "19-08-2024"
  }'
```

### 2. Thống kê với filter đơn vị
```bash
curl -X POST http://localhost:9654/api/v1.0/admin/attendance/daily-statistics \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "startDate": "15-08-2024",
    "endDate": "19-08-2024",
    "unitId": "60f1b2b3c4d5e6f7a8b9c0d1"
  }'
```

## Lưu ý kỹ thuật

### Performance
- API sử dụng MongoDB aggregation để tối ưu hiệu suất
- Dữ liệu được group theo ngày và đơn vị
- Khoảng thời gian tối đa 31 ngày để đảm bảo hiệu suất

### Phân quyền
- Admin có thể xem thống kê của tất cả đơn vị
- User có quyền hạn chế chỉ xem được đơn vị mình quản lý
- Filter `unitId` sẽ được kiểm tra quyền trước khi thực hiện

### Xử lý dữ liệu
- Ngày không có lịch làm việc sẽ hiển thị với tất cả giá trị = 0
- Đơn vị không có cán bộ nào sẽ không xuất hiện trong `byUnit`
- Dữ liệu được sắp xếp theo thứ tự ngày tăng dần

## Test
Sử dụng file `test_daily_statistics_api.js` để test API:
```bash
node test_daily_statistics_api.js
```

Nhớ cập nhật `TEST_TOKEN` trong file test với token hợp lệ.
