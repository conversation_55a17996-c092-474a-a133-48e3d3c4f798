const moment = require('moment');
const _ = require('lodash');

/**
 * Utility functions cho hệ thống thống kê
 * Cung cấp các hàm tiện ích để tính toán, format dữ liệu thống kê
 */
class StatisticsUtils {
  
  /**
   * Tính toán tỷ lệ phần trăm
   * @param {Number} numerator - T<PERSON> số
   * @param {Number} denominator - Mẫu số
   * @param {Number} precision - Số chữ số thập phân (mặc định 2)
   * @returns {Number} Tỷ lệ phần trăm (0-1)
   */
  static calculateRate(numerator, denominator, precision = 2) {
    if (!denominator || denominator === 0) return 0;
    const rate = numerator / denominator;
    return Math.round(rate * Math.pow(10, precision + 2)) / Math.pow(10, precision + 2);
  }

  /**
   * Tính toán phần trăm hiển thị
   * @param {Number} numerator - <PERSON><PERSON> số
   * @param {Number} denominator - Mẫu số
   * @param {Number} precision - Số chữ số thập phân
   * @returns {Number} Phần trăm (0-100)
   */
  static calculatePercentage(numerator, denominator, precision = 1) {
    const rate = this.calculateRate(numerator, denominator, precision);
    return Math.round(rate * 100 * Math.pow(10, precision)) / Math.pow(10, precision);
  }

  /**
   * Chuyển đổi time range thành khoảng thời gian cụ thể
   * @param {String} timeRange - Loại khoảng thời gian: 'day', 'week', 'month', 'custom'
   * @param {String} startDate - Ngày bắt đầu (DD-MM-YYYY) - chỉ dùng cho custom
   * @param {String} endDate - Ngày kết thúc (DD-MM-YYYY) - chỉ dùng cho custom
   * @returns {Object} { startDate, endDate, type }
   */
  static getTimeRange(timeRange = 'day', startDate = null, endDate = null) {
    const now = moment();
    let start, end;

    switch (timeRange) {
      case 'week':
        start = now.clone().startOf('isoWeek'); // Thứ 2
        end = now.clone().endOf('isoWeek'); // Chủ nhật
        break;
      
      case 'month':
        start = now.clone().startOf('month');
        end = now.clone().endOf('month');
        break;
      
      case 'custom':
        if (!startDate || !endDate) {
          throw new Error('Custom time range cần có startDate và endDate');
        }
        start = moment(startDate, 'DD-MM-YYYY');
        end = moment(endDate, 'DD-MM-YYYY');
        
        if (!start.isValid() || !end.isValid()) {
          throw new Error('Định dạng ngày không hợp lệ. Sử dụng DD-MM-YYYY');
        }
        
        if (start.isAfter(end)) {
          throw new Error('Ngày bắt đầu không thể sau ngày kết thúc');
        }
        break;
      
      case 'day':
      default:
        start = now.clone().startOf('day');
        end = now.clone().endOf('day');
        timeRange = 'day';
        break;
    }

    return {
      startDate: start.format('DD-MM-YYYY'),
      endDate: end.format('DD-MM-YYYY'),
      type: timeRange,
      startTimestamp: start.valueOf(),
      endTimestamp: end.valueOf()
    };
  }

  /**
   * Tạo MongoDB query cho khoảng thời gian
   * @param {String} dateField - Tên field chứa ngày trong DB
   * @param {Object} timeRange - Kết quả từ getTimeRange()
   * @param {String} dateFormat - Format của date field: 'timestamp' hoặc 'ddmmyyyy'
   * @returns {Object} MongoDB query object
   */
  static createTimeRangeQuery(dateField, timeRange, dateFormat = 'ddmmyyyy') {
    if (dateFormat === 'timestamp') {
      return {
        [dateField]: {
          $gte: timeRange.startTimestamp,
          $lte: timeRange.endTimestamp
        }
      };
    } else {
      // Format DD-MM-YYYY
      const dates = this.getDatesBetween(timeRange.startDate, timeRange.endDate);
      return {
        [dateField]: { $in: dates }
      };
    }
  }

  /**
   * Lấy danh sách tất cả ngày trong khoảng thời gian
   * @param {String} startDate - Ngày bắt đầu (DD-MM-YYYY)
   * @param {String} endDate - Ngày kết thúc (DD-MM-YYYY)
   * @returns {Array} Mảng các ngày theo format DD-MM-YYYY
   */
  static getDatesBetween(startDate, endDate) {
    const start = moment(startDate, 'DD-MM-YYYY');
    const end = moment(endDate, 'DD-MM-YYYY');
    const dates = [];

    const current = start.clone();
    while (current.isSameOrBefore(end)) {
      dates.push(current.format('DD-MM-YYYY'));
      current.add(1, 'day');
    }

    return dates;
  }

  /**
   * Nhóm dữ liệu theo unit
   * @param {Array} data - Mảng dữ liệu có chứa thông tin unit
   * @param {String} unitField - Tên field chứa thông tin unit
   * @returns {Object} Dữ liệu đã được nhóm theo unit
   */
  static groupByUnit(data, unitField = 'unit') {
    return _.groupBy(data, item => {
      const unit = _.get(item, unitField);
      return unit ? (unit._id || unit) : 'unknown';
    });
  }

  /**
   * Nhóm dữ liệu theo area
   * @param {Array} data - Mảng dữ liệu có chứa thông tin area
   * @param {String} areaField - Tên field chứa thông tin area
   * @returns {Object} Dữ liệu đã được nhóm theo area
   */
  static groupByArea(data, areaField = 'areas') {
    const result = {};
    
    data.forEach(item => {
      const areas = _.get(item, areaField, []);
      
      if (Array.isArray(areas) && areas.length > 0) {
        areas.forEach(area => {
          const areaId = area._id || area;
          if (!result[areaId]) {
            result[areaId] = [];
          }
          result[areaId].push(item);
        });
      } else {
        // Nếu không có area, gán vào nhóm 'unassigned'
        if (!result['unassigned']) {
          result['unassigned'] = [];
        }
        result['unassigned'].push(item);
      }
    });

    return result;
  }

  /**
   * Tính toán thống kê cơ bản cho một nhóm dữ liệu
   * @param {Array} data - Mảng dữ liệu
   * @param {Object} options - Tùy chọn tính toán
   * @returns {Object} Kết quả thống kê
   */
  static calculateBasicStats(data, options = {}) {
    const {
      totalField = null,
      countField = null,
      statusField = 'status',
      statusValues = {}
    } = options;

    const total = data.length;
    const result = {
      total,
      count: total
    };

    // Tính tổng theo field cụ thể
    if (totalField) {
      result.sum = _.sumBy(data, totalField);
      result.average = total > 0 ? result.sum / total : 0;
    }

    // Đếm theo field cụ thể
    if (countField) {
      result.countBy = _.countBy(data, countField);
    }

    // Thống kê theo status
    if (statusField) {
      const statusCounts = _.countBy(data, statusField);
      result.byStatus = statusCounts;
      
      // Tính tỷ lệ cho các status được định nghĩa
      Object.keys(statusValues).forEach(status => {
        const count = statusCounts[status] || 0;
        result[`${status}Count`] = count;
        result[`${status}Rate`] = this.calculateRate(count, total);
      });
    }

    return result;
  }

  /**
   * Format dữ liệu cho biểu đồ
   * @param {Object} data - Dữ liệu thống kê
   * @param {String} chartType - Loại biểu đồ: 'pie', 'bar', 'line'
   * @returns {Object} Dữ liệu đã format cho chart
   */
  static formatForChart(data, chartType = 'bar') {
    switch (chartType) {
      case 'pie':
        return this.formatForPieChart(data);
      case 'line':
        return this.formatForLineChart(data);
      case 'bar':
      default:
        return this.formatForBarChart(data);
    }
  }

  /**
   * Format dữ liệu cho pie chart
   * @param {Object} data - Dữ liệu có dạng { label: value }
   * @returns {Array} Mảng { name, value } cho pie chart
   */
  static formatForPieChart(data) {
    return Object.keys(data).map(key => ({
      name: key,
      value: data[key]
    }));
  }

  /**
   * Format dữ liệu cho bar chart
   * @param {Object} data - Dữ liệu có dạng { label: value }
   * @returns {Object} { labels, values } cho bar chart
   */
  static formatForBarChart(data) {
    return {
      labels: Object.keys(data),
      values: Object.values(data)
    };
  }

  /**
   * Format dữ liệu cho line chart
   * @param {Array} data - Mảng dữ liệu theo thời gian
   * @param {String} xField - Field cho trục X (thường là thời gian)
   * @param {String} yField - Field cho trục Y (giá trị)
   * @returns {Array} Mảng { x, y } cho line chart
   */
  static formatForLineChart(data, xField = 'date', yField = 'value') {
    return data.map(item => ({
      x: item[xField],
      y: item[yField]
    }));
  }

  /**
   * Tạo cache key cho Redis
   * @param {String} prefix - Prefix của key
   * @param {Array} params - Các tham số để tạo key
   * @returns {String} Cache key
   */
  static generateCacheKey(prefix, ...params) {
    const cleanParams = params.filter(p => p !== null && p !== undefined);
    return `statistics:${prefix}:${cleanParams.join(':')}`;
  }

  /**
   * Validate tham số đầu vào cho API thống kê
   * @param {Object} params - Tham số cần validate
   * @returns {Object} { isValid, errors }
   */
  static validateStatisticsParams(params) {
    const errors = [];
    const { timeRange, startDate, endDate } = params;

    // Validate time range
    const validTimeRanges = ['day', 'week', 'month', 'custom'];
    if (timeRange && !validTimeRanges.includes(timeRange)) {
      errors.push(`timeRange phải là một trong: ${validTimeRanges.join(', ')}`);
    }

    // Validate custom date range
    if (timeRange === 'custom') {
      if (!startDate || !endDate) {
        errors.push('Custom time range cần có startDate và endDate');
      } else {
        const start = moment(startDate, 'DD-MM-YYYY', true);
        const end = moment(endDate, 'DD-MM-YYYY', true);
        
        if (!start.isValid()) {
          errors.push('startDate phải có định dạng DD-MM-YYYY');
        }
        if (!end.isValid()) {
          errors.push('endDate phải có định dạng DD-MM-YYYY');
        }
        if (start.isValid() && end.isValid() && start.isAfter(end)) {
          errors.push('startDate không thể sau endDate');
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

module.exports = StatisticsUtils;
