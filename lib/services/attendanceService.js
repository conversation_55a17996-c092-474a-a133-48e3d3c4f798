/**
 * Service xử lý logic điểm danh
 * Quản lý điểm danh, lịch sử và thống kê chuyên cần
 */

const _ = require('lodash');
const AttendanceRecord = require('../models/attendanceRecord');
const WorkSchedule = require('../models/workSchedule');
const User = require('../models/user');
const LeaveRequest = require('../models/leaveRequest');
const attendancePermission = require('../util/attendancePermission');
const NotificationHelper = require('../util/notificationHelper');
const attendanceCache = require('../util/attendanceCache');
const DateUtils = require('../utils/dateUtils');

class AttendanceService {
  /**
   * Điểm danh cho cán bộ
   * @param {String} userId - ID cán bộ
   * @param {Object} location - Vị trí điểm danh (optional)
   * @returns {Object} Kết quả điểm danh
   */
  async checkin(userId, location = null) {
    try {
      const currentDate = DateUtils.getCurrentDateDDMMYYYY();

      // Tìm lịch làm việc
      const schedule = await WorkSchedule.findOne({
        date: currentDate,
        user: userId,
        status: 1
      });
      if (!schedule) {
        return {
          success: false,
          message: {
            head: 'Không có lịch làm việc',
            body: 'Không có lịch làm việc cho ngày hôm nay'
          },
          data: null
        };
      }

      // Tìm ca làm việc hiện tại
      const currentTime = new Date();
      const currentShift = this.getCurrentShift(schedule.shifts, currentTime);
      if (!currentShift) {
        return {
          success: false,
          message: {
            head: 'Không có ca làm việc',
            body: 'Không có ca làm việc nào trong thời gian hiện tại'
          },
          data: null
        };
      }

      // Kiểm tra đã điểm danh chưa
      const existingRecord = await AttendanceRecord.findOne({
        user: userId,
        date: currentDate,
        shift: currentShift.type
      });

      if (existingRecord) {
        return {
          success: false,
          message: {
            head: 'Đã điểm danh',
            body: 'Bạn đã điểm danh cho ca làm việc này'
          },
          data: existingRecord
        };
      }

      // Xác định trạng thái điểm danh
      const status = this.determineAttendanceStatus(currentShift, currentTime);

      // Tạo bản ghi điểm danh
      const attendanceData = {
        user: userId,
        schedule: schedule._id,
        date: currentDate,
        shift: currentShift.type,
        checkinTime: currentTime.getTime(),
        status: status,
        location: location
      };

      const attendanceRecord = await AttendanceRecord.create(attendanceData);

      // Cập nhật trạng thái ca làm việc trong schedule
      const shiftIndex = schedule.shifts.findIndex(s => s.type === currentShift.type);
      if (shiftIndex !== -1) {
        schedule.shifts[shiftIndex].status = status;
        schedule.shifts[shiftIndex].checkinTime = currentTime.getTime();
        if (location) {
          schedule.shifts[shiftIndex].location = location;
        }
        schedule.updatedAt = Date.now();
        await schedule.save();
      }

      // Gửi thông báo điểm danh thành công
      NotificationHelper.notifySuccessfulCheckin(userId, {
        status,
        shift: currentShift.type,
        checkinTime: currentTime.getTime()
      });

      // Invalidate cache
      attendanceCache.invalidateTodayStatus(userId, currentDate);
      attendanceCache.invalidateAttendanceStats(userId);

      return {
        success: true,
        message: status === 'on_time' ? {
          head: 'Điểm danh thành công',
          body: 'Bạn đã điểm danh thành công'
        } : {
          head: 'Điểm danh muộn',
          body: 'Bạn đã điểm danh muộn'
        },
        data: {
          attendanceRecord,
          status,
          checkinTime: currentTime.getTime()
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Lấy lịch sử điểm danh
   * @param {String} userId - ID cán bộ
   * @param {String} startDate - Ngày bắt đầu
   * @param {String} endDate - Ngày kết thúc
   * @returns {Object} Lịch sử điểm danh
   */
  async getAttendanceHistory(userId, startDate, endDate) {
    try {
      const query = {
        user: userId
      };

      if (startDate && endDate) {
        query.date = {
          $gte: startDate,
          $lte: endDate
        };
      } else if (startDate) {
        query.date = { $gte: startDate };
      } else if (endDate) {
        query.date = { $lte: endDate };
      }

      const records = await AttendanceRecord.find(query)
        .populate('user', 'name idNumber')
        .populate('schedule')
        .sort({ date: -1, checkinTime: -1 })
        .lean();

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy lịch sử điểm danh thành công'
        },
        data: records
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Kiểm tra trạng thái điểm danh hôm nay
   * @param {String} userId - ID cán bộ
   * @param {String} date - Ngày kiểm tra (DD-MM-YYYY)
   * @param {String} shift - Ca làm việc (optional)
   * @returns {Object} Trạng thái điểm danh
   */
  async checkAttendanceStatus(userId, date, shift = null) {
    try {
      const query = {
        user: userId,
        date: date
      };

      if (shift) {
        query.shift = shift;
      }

      const records = await AttendanceRecord.find(query)
        .populate('schedule')
        .lean();

      // Lấy lịch làm việc của ngày đó
      const schedule = await WorkSchedule.findOne({
        user: userId,
        date: date,
        status: 1
      }).lean();

      const result = {
        date,
        hasSchedule: !!schedule,
        shifts: [],
        summary: {
          total: 0,
          completed: 0,
          missed: 0,
          pending: 0
        }
      };

      if (schedule) {
        result.shifts = schedule.shifts.map(scheduleShift => {
          const attendanceRecord = records.find(r => r.shift === scheduleShift.type);
          const shiftStatus = this.getShiftStatus(scheduleShift, attendanceRecord, date);

          result.summary.total++;
          result.summary[shiftStatus]++;

          return {
            type: scheduleShift.type,
            startTime: scheduleShift.startTime,
            status: shiftStatus,
            checkinTime: attendanceRecord?.checkinTime,
            attendanceStatus: attendanceRecord?.status
          };
        });
      }

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Kiểm tra trạng thái điểm danh thành công'
        },
        data: result
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê chuyên cần
   * @param {String} userId - ID cán bộ
   * @param {String} startDate - Ngày bắt đầu
   * @param {String} endDate - Ngày kết thúc
   * @returns {Object} Thống kê chuyên cần
   */
  async getAttendanceStatistics(userId, startDate, endDate) {
    try {
      // Lấy dữ liệu điểm danh
      const query = {
        user: userId,
        date: {
          $gte: startDate,
          $lte: endDate
        }
      };

      const [attendanceRecords, workSchedules] = await Promise.all([
        AttendanceRecord.find(query).populate('user', 'name idNumber units').lean(),
        WorkSchedule.find({
          user: userId,
          date: {
            $gte: startDate,
            $lte: endDate
          },
          status: 1
        }).populate('user', 'name idNumber units').lean()
      ]);

      // Tính toán thống kê
      const statistics = await this.calculateStatistics(attendanceRecords, workSchedules);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê chuyên cần thành công'
        },
        data: {
          period: { startDate, endDate },
          statistics: statistics[0]
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê chuyên cần (Admin)
   * @param {String} userId - ID cán bộ (optional, nếu không có thì thống kê theo quyền)
   * @param {String} unitId - ID đơn vị (optional)
   * @param {String} startDate - Ngày bắt đầu
   * @param {String} endDate - Ngày kết thúc
   * @param {String} viewerId - ID người xem thống kê
   * @returns {Object} Thống kê chuyên cần
   */
  async getAttendanceStatisticsAdmin(viewerId, userId = null, unitId = null, startDate, endDate) {
    try {
      // Kiểm tra quyền xem thống kê
      const permissionCheck = await attendancePermission.checkStatisticsPermission(viewerId, userId);

      if (!permissionCheck.canView) {
        return {
          success: false,
          message: {
            head: 'Không có quyền',
            body: permissionCheck.message
          },
          data: null
        };
      }

      let targetUserIds = [];

      if (userId) {
        // Thống kê cho một cán bộ cụ thể
        // targetUserIds = [userId];
      } else {
        // Thống kê cho nhiều cán bộ theo quyền
        if (permissionCheck.scope === 'all') {
          // Lấy tất cả cán bộ
          const allUsers = await User.find({ status: 1 }).select('_id').lean();
          targetUserIds = allUsers.map(u => u._id.toString());
        } else {
          // Lấy cán bộ cùng đơn vị
          targetUserIds = await attendancePermission.getManagedUsers(viewerId);
        }

        // Lọc theo đơn vị nếu có
        if (unitId) {
          const unitUsers = await User.find({
            units: unitId,
            status: 1
          }).select('_id').lean();
          const unitUserIds = unitUsers.map(u => u._id.toString());
          targetUserIds = targetUserIds.filter(id => unitUserIds.includes(id));
        }
      }

      // Lấy dữ liệu điểm danh
      const query = {
        // user: { $in: targetUserIds },
        date: {
          $gte: startDate,
          $lte: endDate
        }
      };
      if (targetUserIds.length) {
        query.user = { $in: targetUserIds };
      }

      const [attendanceRecords, workSchedules] = await Promise.all([
        AttendanceRecord.find(query).populate('user', 'name idNumber units').lean(),
        WorkSchedule.find({
          user: { $in: targetUserIds },
          date: {
            $gte: startDate,
            $lte: endDate
          },
          status: 1
        }).populate('user', 'name idNumber units').lean()
      ]);

      // Tính toán thống kê
      const statistics = await this.calculateStatistics(attendanceRecords, workSchedules);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê chuyên cần thành công'
        },
        data: {
          period: { startDate, endDate },
          scope: permissionCheck.scope,
          statistics
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Tìm ca làm việc hiện tại
   * @param {Array} shifts - Danh sách ca làm việc
   * @param {Date} currentTime - Thời gian hiện tại
   * @returns {Object|null} Ca làm việc hiện tại
   */
  getCurrentShift(shifts, currentTime) {
    const currentHour = currentTime.getHours();
    const currentMinute = currentTime.getMinutes();
    const currentTimeInMinutes = currentHour * 60 + currentMinute;

    // Ca sáng: 8:00 (có thể điểm danh từ 7:30 đến 12:00)
    // Ca chiều: 14:00 (có thể điểm danh từ 13:30 đến 18:00)

    for (const shift of shifts) {
      if (shift.type === 'morning') {
        // Ca sáng: 7:30 - 12:00
        if (currentTimeInMinutes >= 7 * 60 + 30 && currentTimeInMinutes <= 12 * 60) { // 7:30 - 12:00
          return shift;
        }
      } else if (shift.type === 'afternoon') {
        // Ca chiều: 13:30 - 18:00
        if (currentTimeInMinutes >= 13 * 60 + 30 && currentTimeInMinutes <= 18 * 60) { // 13:30 - 18:00
          return shift;
        }
      }
    }

    return null;
  }

  /**
   * Xác định trạng thái điểm danh
   * @param {Object} shift - Ca làm việc
   * @param {Date} checkinTime - Thời gian điểm danh
   * @returns {String} Trạng thái điểm danh
   */
  determineAttendanceStatus(shift, checkinTime) {
    const checkinHour = checkinTime.getHours();
    const checkinMinute = checkinTime.getMinutes();
    const checkinTimeInMinutes = checkinHour * 60 + checkinMinute;

    let workStartTime;
    if (shift.type === 'morning') {
      workStartTime = 8 * 60; // 8:00
    } else if (shift.type === 'afternoon') {
      workStartTime = 14 * 60; // 14:00
    }

    return checkinTimeInMinutes <= workStartTime ? 'on_time' : 'late';
  }

  /**
   * Lấy trạng thái ca làm việc (đã cải tiến để phân biệt missed và pending)
   * @param {Object} scheduleShift - Ca trong lịch làm việc
   * @param {Object} attendanceRecord - Bản ghi điểm danh
   * @param {String} scheduleDate - Ngày của ca làm việc (DD-MM-YYYY)
   * @returns {String} Trạng thái ca
   */
  getShiftStatus(scheduleShift, attendanceRecord, scheduleDate = null) {
    if (attendanceRecord) {
      return 'completed';
    }

    // Nếu không có ngày, sử dụng logic cũ
    if (!scheduleDate) {
      const now = new Date();
      const currentHour = now.getHours();
      const workStartHour = scheduleShift.type === 'morning' ? 8 : 14;

      if (currentHour > workStartHour + 4) {
        return 'missed';
      }
      return 'pending';
    }

    // Sử dụng logic mới với ngày cụ thể
    if (this.isShiftCompleted(scheduleDate, scheduleShift.type)) {
      return 'missed'; // Ca đã kết thúc mà không có chấm công
    }

    return 'pending'; // Ca chưa kết thúc
  }

  /**
   * Tính toán thống kê từ dữ liệu (đã cải tiến để phân biệt absent và nonattendance)
   * @param {Array} attendanceRecords - Bản ghi điểm danh
   * @param {Array} workSchedules - Lịch làm việc
   * @returns {Object} Thống kê
   */
  async calculateStatistics(attendanceRecords, workSchedules) {
    const userStats = {};

    // Khởi tạo thống kê cho từng user
    workSchedules.forEach(schedule => {
      const userId = schedule.user._id.toString();
      if (!userStats[userId]) {
        userStats[userId] = {
          user: schedule.user,
          totalScheduled: 0,
          totalAttended: 0,
          onTime: 0,
          late: 0,
          absent: 0,
          nonattendance: 0,
          excused: 0, // Thêm trường mới cho đơn nghỉ được phê duyệt
          attendanceRate: 0
        };
      }
      userStats[userId].totalScheduled += schedule.shifts.length;
    });

    // Tính toán từ bản ghi điểm danh
    attendanceRecords.forEach(record => {
      const userId = record.user._id.toString();
      if (userStats[userId]) {
        userStats[userId].totalAttended++;

        if (record.status === 'on_time') {
          userStats[userId].onTime++;
        } else if (record.status === 'late') {
          userStats[userId].late++;
        }
      }
    });

    // Tính toán absent, nonattendance và excused cho từng user
    for (const userId of Object.keys(userStats)) {
      const stats = userStats[userId];
      const userSchedules = workSchedules.filter(s => s.user._id.toString() === userId);

      // Lấy danh sách đơn xin nghỉ được phê duyệt cho user này
      const approvedLeaves = await this.getApprovedLeaveRequests(userId, userSchedules);

      let absentCount = 0;
      let nonattendanceCount = 0;
      let excusedCount = 0;

      // Debug: Uncomment để kiểm tra dữ liệu khi cần debug
      // console.log(`\n=== DEBUG USER ${userId} ===`);
      // console.log(`Total scheduled: ${stats.totalScheduled}`);
      // console.log(`Total attended: ${stats.totalAttended}`);
      // console.log(`User schedules count: ${userSchedules.length}`);
      // console.log(`Approved leaves count: ${approvedLeaves.length}`);

      // Duyệt qua từng ca làm việc để phân loại
      for (const schedule of userSchedules) {
        for (const shift of schedule.shifts) {
          const hasAttendance = attendanceRecords.some(record =>
            record.user._id.toString() === userId &&
            record.date === schedule.date &&
            record.shift === shift.type
          );

          // console.log(`Shift ${schedule.date} ${shift.type}: hasAttendance=${hasAttendance}, status=${shift.status}`);

          if (!hasAttendance) {
            // Ưu tiên sử dụng trạng thái từ WorkSchedule nếu có
            if (shift.status === 'excused') {
              excusedCount++;
              // console.log(`  -> EXCUSED from WorkSchedule (count: ${excusedCount})`);
              continue;
            } else if (shift.status === 'missed') {
              absentCount++;
              // console.log(`  -> ABSENT from WorkSchedule (count: ${absentCount})`);
              continue;
            }

            // Fallback: Tính toán theo logic cũ nếu WorkSchedule chưa có trạng thái
            const hasApprovedLeave = this.hasApprovedLeaveForShift(
              approvedLeaves,
              schedule.date,
              shift.type
            );

            // console.log(`  -> hasApprovedLeave=${hasApprovedLeave}`);

            if (hasApprovedLeave) {
              // Có đơn xin nghỉ được phê duyệt -> tính vào excused
              excusedCount++;
              // console.log(`  -> EXCUSED (count: ${excusedCount})`);
              continue;
            }

            // Kiểm tra ca làm việc đã kết thúc chưa
            const isCompleted = this.isShiftCompleted(schedule.date, shift.type);
            // console.log(`  -> isShiftCompleted=${isCompleted}`);

            if (isCompleted) {
              absentCount++; // Ca đã kết thúc mà không có chấm công -> absent
              // console.log(`  -> ABSENT (count: ${absentCount})`);
            } else {
              nonattendanceCount++; // Ca chưa kết thúc -> nonattendance
              // console.log(`  -> NONATTENDANCE (count: ${nonattendanceCount})`);
            }
          }
        }
      }

      stats.absent = absentCount;
      stats.nonattendance = nonattendanceCount;
      stats.excused = excusedCount;
      stats.attendanceRate = stats.totalScheduled > 0
        ? Math.round((stats.totalAttended / stats.totalScheduled) * 100)
        : 0;

      // Debug: Uncomment để kiểm tra tổng khi cần debug
      // const total = stats.totalAttended + stats.absent + stats.nonattendance + stats.excused;
      // console.log(`Final counts: attended=${stats.totalAttended}, absent=${stats.absent}, nonattendance=${stats.nonattendance}, excused=${stats.excused}`);
      // console.log(`Total check: ${total} === ${stats.totalScheduled} ? ${total === stats.totalScheduled}`);
      // if (total !== stats.totalScheduled) {
      //   console.warn(`⚠️  MISMATCH: Total (${total}) !== totalScheduled (${stats.totalScheduled})`);
      // }
    }

    return Object.values(userStats);
  }

  /**
   * Lấy danh sách đơn xin nghỉ được phê duyệt cho user trong khoảng thời gian
   * @param {String} userId - ID user
   * @param {Array} schedules - Danh sách lịch làm việc
   * @returns {Array} Danh sách đơn xin nghỉ được phê duyệt
   */
  async getApprovedLeaveRequests(userId, schedules) {
    if (!schedules || schedules.length === 0) {
      return [];
    }

    // Lấy khoảng thời gian từ schedules
    const dates = schedules.map(s => s.date);
    const startDate = dates.sort()[0];
    const endDate = dates.sort().reverse()[0];

    try {
      const approvedLeaves = await LeaveRequest.find({
        user: userId,
        status: 'approved',
        $or: [
          // Đơn nghỉ phép có khoảng thời gian
          {
            type: 'leave',
            startDate: { $lte: endDate },
            endDate: { $gte: startDate }
          },
          // Đơn nghỉ đột xuất hoặc đi muộn trong ngày cụ thể
          {
            type: { $in: ['emergency_leave', 'late_arrival'] },
            startDate: { $gte: startDate, $lte: endDate }
          }
        ]
      }).lean();

      return approvedLeaves;
    } catch (error) {
      console.error('Error getting approved leave requests:', error);
      return [];
    }
  }

  /**
   * Kiểm tra có đơn xin nghỉ được phê duyệt cho ca làm việc cụ thể
   * @param {Array} approvedLeaves - Danh sách đơn xin nghỉ được phê duyệt
   * @param {String} date - Ngày (DD-MM-YYYY)
   * @param {String} shiftType - Loại ca ('morning' | 'afternoon')
   * @returns {Boolean} Có đơn xin nghỉ được phê duyệt hay không
   */
  hasApprovedLeaveForShift(approvedLeaves, date, shiftType) {
    return approvedLeaves.some(leave => {
      // Đơn nghỉ phép (có thể nhiều ngày)
      if (leave.type === 'leave') {
        return DateUtils.compareDDMMYYYY(leave.startDate, date) <= 0 &&
               DateUtils.compareDDMMYYYY(leave.endDate, date) >= 0;
      }

      // Đơn nghỉ đột xuất hoặc đi muộn (trong ngày)
      if (leave.type === 'emergency_leave' || leave.type === 'late_arrival') {
        if (leave.startDate !== date) {
          return false;
        }

        // Kiểm tra ca làm việc
        if (leave.shift === 'both') {
          return true; // Nghỉ cả ngày
        }

        return leave.shift === shiftType;
      }

      return false;
    });
  }

  /**
   * Kiểm tra ca làm việc đã kết thúc chưa
   * @param {String} date - Ngày (DD-MM-YYYY)
   * @param {String} shiftType - Loại ca ('morning' | 'afternoon')
   * @returns {Boolean} Ca làm việc đã kết thúc hay chưa
   */
  isShiftCompleted(date, shiftType) {
    const now = new Date();
    const currentDate = DateUtils.getCurrentDateDDMMYYYY();

    // Nếu là ngày trong tương lai, chưa kết thúc
    if (DateUtils.compareDDMMYYYY(date, currentDate) > 0) {
      return false;
    }

    // Nếu là ngày trong quá khứ, đã kết thúc
    if (DateUtils.compareDDMMYYYY(date, currentDate) < 0) {
      return true;
    }

    // Nếu là ngày hôm nay, kiểm tra thời gian
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentTimeInMinutes = currentHour * 60 + currentMinute;

    // Thời gian kết thúc ca làm việc
    let shiftEndTime;
    if (shiftType === 'morning') {
      shiftEndTime = 12 * 60; // 12:00
    } else if (shiftType === 'afternoon') {
      shiftEndTime = 18 * 60; // 18:00
    }

    return currentTimeInMinutes > shiftEndTime;
  }
}

module.exports = new AttendanceService();