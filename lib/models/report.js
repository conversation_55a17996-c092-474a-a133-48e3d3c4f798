const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema

const ReportSchema = new mongoose.Schema(
  {
    // Thông tin cơ bản
    title: {
      type: String
    },
    description: {
      type: String
    },
    // Liên kết với JobType (chính là loại báo cáo)
    jobType: {
      type: Schema.Types.ObjectId,
      ref: 'JobType',
      required: true
    },

    // Dữ liệu số liệu (chỉ lưu số lượng)
    metrics: {
      type: Schema.Types.Mixed,
      default: {}
    },

    // Chi tiết báo cáo (cho các báo cáo cần thời gian/địa điểm chi tiết)
    details: [{
      time: {
        type: Number // milliseconds timestamp
      },
      location: {
        address: String,
        coordinates: {
          type: [Number], // GeoJSON format: [lng, lat]
          index: '2dsphere'
        },
        // Giữ lại format cũ để tương thích
        lat: Number,
        lng: Number,
        area: {
          type: Schema.Types.ObjectId,
          ref: 'Area'
        }
      }
    }],

    // Người tạo báo cáo
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },

    // Đơn vị
    unit: {
      type: Schema.Types.ObjectId,
      ref: 'Unit'
    },

    // Trạng thái
    status: {
      type: String,
      enum: ['draft', 'submitted', 'approved', 'rejected'],
      default: 'draft'
    },

    // Metadata
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    deletedAt: {
      type: Number
    }
  },
  { id: false, versionKey: false }
)

// Indexes cho performance
ReportSchema.index({ jobType: 1, createdAt: 1 })
// Bỏ 2dsphere index vì format coordinates không chuẩn GeoJSON
ReportSchema.index({ 'details.location.coordinates': '2dsphere' })
ReportSchema.index({ createdBy: 1, createdAt: -1 })
ReportSchema.index({ unit: 1, createdAt: -1 })

module.exports = mongoConnections("master").model("Report", ReportSchema)