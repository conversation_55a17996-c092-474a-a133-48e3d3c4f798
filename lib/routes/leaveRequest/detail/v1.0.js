const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const leaveService = require('../../../services/leaveService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API lấy chi tiết đơn xin nghỉ phép
 * POST /api/v1.0/leave-request/detail
 */
module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { requestId } = req.body;

  const validateParams = (next) => {
    const schema = Joi.object({
      requestId: Joi.objectId().required()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getRequestDetail = (next) => {
    try {
      leaveService.getLeaveRequestDetail(requestId, userId)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: res.message,
            data: res.data
          });
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  async.waterfall([
    validateParams,
    getRequestDetail
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
