const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const attendanceService = require('../../../services/attendanceService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const moment = require('moment');

/**
 * API kiểm tra trạng thái điểm danh
 * POST /api/v1.0/attendance/status
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    date,
    shift
  } = req.body;

  const validateParams = (next) => {
    const schema = Joi.object({
      // Chỉ chấp nhận định dạng DD-MM-YYYY
      date: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      shift: Joi.string().valid('morning', 'afternoon').optional()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const checkStatus = (next) => {
    try {
      // Nếu không có date thì lấy ngày hôm nay
      const checkDate = date || moment().format('DD-MM-YYYY');

      attendanceService.checkAttendanceStatus(userId, checkDate, shift)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: res.message,
            data: res.data
          });
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  async.waterfall([
    validateParams,
    checkStatus
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};