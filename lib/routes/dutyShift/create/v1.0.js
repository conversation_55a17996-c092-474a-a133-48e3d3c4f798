const _ = require('lodash');
const async = require('async');
const StatisticsTrigger = require('../../../utils/statisticsTrigger');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyShiftModel = require('../../../models/dutyShift');
const UserModel = require('../../../models/user');
const DutySpecializedScheduleModel = require('../../../models/dutySpecializedSchedule');
const DutyCriminalScheduleModel = require('../../../models/dutyCriminalSchedule');
const DutyMainScheduleModel = require('../../../models/dutyMainSchedule');
const DutySubScheduleModel = require('../../../models/dutySubSchedule');
const DutyLocationScheduleModel = require('../../../models/dutyLocationSchedule');
const DutyPatrolScheduleModel = require('../../../models/dutyPatrolSchedule');
const DutyStadiumScheduleModel = require('../../../models/dutyStadiumSchedule');
const DutyEmergencyScheduleModel = require('../../../models/dutyEmergencySchedule');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  let {
    startTime,
    endTime,
    officer,
    name,
    forLeader,
    locationDuty,
    unit,
    description,
    notes,
    hasEquipment,
    dutySpecializedSchedule,
    dutyCriminalSchedule,
    dutyMainSchedule,
    dutySubSchedule,
    dutyLocationSchedule,
    dutyPatrolSchedule,
    dutyStadiumSchedule,
    dutyEmergencySchedule
  } = req.body;

  forLeader = forLeader || false;

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Kiểm tra các trường bắt buộc
    if (!startTime) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp thời gian bắt đầu ca trực'
        }
      });
    }

    if (!endTime) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp thời gian kết thúc ca trực'
        }
      });
    }

    if (!officer) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng chọn cán bộ trực'
        }
      });
    }

    if (!name) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp tên ca trực'
        }
      });
    }

    // Validate startTime và endTime
    if (typeof startTime !== 'number' || typeof endTime !== 'number') {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Thời gian bắt đầu và kết thúc phải là số (timestamp)'
        }
      });
    }

    if (startTime >= endTime) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc'
        }
      });
    }

    next();
  };

  const checkOfficerExists = (next) => {
    UserModel.findOne({ _id: officer, status: 1 })
      .lean()
      .exec((err, user) => {
        if (err) {
          return next(err);
        }

        if (!user) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Cán bộ trực không tồn tại hoặc đã bị vô hiệu hóa'
            }
          });
        }

        next();
      });
  };

  const checkDutySpecializedSchedule = (next) => {
    // Nếu không có dutySpecializedSchedule thì bỏ qua validation này
    if (!dutySpecializedSchedule) {
      return next();
    }

    DutySpecializedScheduleModel.findOne({ _id: dutySpecializedSchedule, status: 1 })
      .lean()
      .exec((err, schedule) => {
        if (err) {
          return next(err);
        }

        if (!schedule) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Lịch trực không tồn tại hoặc đã bị vô hiệu hóa'
            }
          });
        }

        // Validate thời gian ca trực phải nằm trong khoảng thời gian của schedule
        if (startTime < schedule.startTime || endTime > schedule.endTime) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Thời gian ca trực phải nằm trong khoảng thời gian của lịch trực'
            }
          });
        }

        // Validate startTime phải nhỏ hơn endTime của schedule
        if (startTime >= schedule.endTime || endTime <= schedule.startTime) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Thời gian ca trực không hợp lệ với lịch trực'
            }
          });
        }

        next();
      });
  };

  const checkDutyCriminalSchedule = (next) => {
    // Nếu không có dutyCriminalSchedule thì bỏ qua validation này
    if (!dutyCriminalSchedule) {
      return next();
    }

    DutyCriminalScheduleModel.findOne({ _id: dutyCriminalSchedule, status: 1 })
      .lean()
      .exec((err, schedule) => {
        if (err) {
          return next(err);
        }

        if (!schedule) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Lịch trực không tồn tại hoặc đã bị vô hiệu hóa'
            }
          });
        }

        // Validate thời gian ca trực phải nằm trong khoảng thời gian của schedule
        if (startTime < schedule.startTime || endTime > schedule.endTime) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Thời gian ca trực phải nằm trong khoảng thời gian của lịch trực'
            }
          });
        }

        // Validate startTime phải nhỏ hơn endTime của schedule
        if (startTime >= schedule.endTime || endTime <= schedule.startTime) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Thời gian ca trực không hợp lệ với lịch trực'
            }
          });
        }

        next();
      });
  };

  const checkDutyMainSchedule = (next) => {
    // Nếu không có dutyMainSchedule thì bỏ qua validation này
    if (!dutyMainSchedule) {
      return next();
    }

    DutyMainScheduleModel.findOne({ _id: dutyMainSchedule, status: 1 })
      .lean()
      .exec((err, schedule) => {
        if (err) {
          return next(err);
        }

        if (!schedule) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Lịch trực không tồn tại hoặc đã bị vô hiệu hóa'
            }
          });
        }

        // Validate thời gian ca trực phải nằm trong khoảng thời gian của schedule
        if (startTime < schedule.startTime || endTime > schedule.endTime) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Thời gian ca trực phải nằm trong khoảng thời gian của lịch trực'
            }
          });
        }

        // Validate startTime phải nhỏ hơn endTime của schedule
        if (startTime >= schedule.endTime || endTime <= schedule.startTime) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Thời gian ca trực không hợp lệ với lịch trực'
            }
          });
        }

        next();
      });
  };

  const checkDutySubSchedule = (next) => {
    // Nếu không có dutySubSchedule thì bỏ qua validation này
    if (!dutySubSchedule) {
      return next();
    }

    DutySubScheduleModel.findOne({ _id: dutySubSchedule, status: 1 })
      .lean()
      .exec((err, schedule) => {
        if (err) {
          return next(err);
        }

        if (!schedule) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Lịch trực không tồn tại hoặc đã bị vô hiệu hóa'
            }
          });
        }

        // Validate thời gian ca trực phải nằm trong khoảng thời gian của schedule
        if (startTime < schedule.startTime || endTime > schedule.endTime) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Thời gian ca trực phải nằm trong khoảng thời gian của lịch trực'
            }
          });
        }

        // Validate startTime phải nhỏ hơn endTime của schedule
        if (startTime >= schedule.endTime || endTime <= schedule.startTime) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Thời gian ca trực không hợp lệ với lịch trực'
            }
          });
        }

        next();
      });
  };

  const checkDutyLocationSchedule = (next) => {
    // Nếu không có dutyLocationSchedule thì bỏ qua validation này
    if (!dutyLocationSchedule) {
      return next();
    }

    DutyLocationScheduleModel.findOne({ _id: dutyLocationSchedule, status: 1 })
      .lean()
      .exec((err, schedule) => {
        if (err) {
          return next(err);
        }

        if (!schedule) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Lịch trực không tồn tại hoặc đã bị vô hiệu hóa'
            }
          });
        }

        // Validate thời gian ca trực phải nằm trong khoảng thời gian của schedule
        if (startTime < schedule.startTime || endTime > schedule.endTime) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Thời gian ca trực phải nằm trong khoảng thời gian của lịch trực'
            }
          });
        }

        // Validate startTime phải nhỏ hơn endTime của schedule
        if (startTime >= schedule.endTime || endTime <= schedule.startTime) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Thời gian ca trực không hợp lệ với lịch trực'
            }
          });
        }

        next();
      });
  };

  const checkDutyPatrolSchedule = (next) => {
    // Nếu không có dutyPatrolSchedule thì bỏ qua validation này
    if (!dutyPatrolSchedule) {
      return next();
    }

    DutyPatrolScheduleModel.findOne({ _id: dutyPatrolSchedule, status: 1 })
      .lean()
      .exec((err, schedule) => {
        if (err) {
          return next(err);
        }

        if (!schedule) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'lịch tuần tra không tồn tại hoặc đã bị vô hiệu hóa'
            }
          });
        }

        // Validate thời gian ca trực phải nằm trong khoảng thời gian của schedule
        if (startTime < schedule.startTime || endTime > schedule.endTime) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Thời gian ca trực phải nằm trong khoảng thời gian của lịch tuần tra'
            }
          });
        }

        // Validate startTime phải nhỏ hơn endTime của schedule
        if (startTime >= schedule.endTime || endTime <= schedule.startTime) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Thời gian ca trực không hợp lệ với lịch tuần tra'
            }
          });
        }

        next();
      });
  };

  const checkDutyStadiumSchedule = (next) => {
    // Nếu không có dutyStadiumSchedule thì bỏ qua validation này
    if (!dutyStadiumSchedule) {
      return next();
    }

    DutyStadiumScheduleModel.findOne({ _id: dutyStadiumSchedule, status: 1 })
      .lean()
      .exec((err, schedule) => {
        if (err) {
          return next(err);
        }

        if (!schedule) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Lịch trực sân vận động không tồn tại hoặc đã bị vô hiệu hóa'
            }
          });
        }

        // Validate thời gian ca trực phải nằm trong khoảng thời gian của schedule
        if (startTime < schedule.startTime || endTime > schedule.endTime) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Thời gian ca trực phải nằm trong khoảng thời gian của lịch trực sân vận động'
            }
          });
        }

        // Validate startTime phải nhỏ hơn endTime của schedule
        if (startTime >= schedule.endTime || endTime <= schedule.startTime) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Thời gian ca trực không hợp lệ với lịch trực sân vận động'
            }
          });
        }

        next();
      });
  };

  const checkDutyEmergencySchedule = (next) => {
    // Nếu không có dutyEmergencySchedule thì bỏ qua validation này
    if (!dutyEmergencySchedule) {
      return next();
    }

    DutyEmergencyScheduleModel.findOne({ _id: dutyEmergencySchedule, status: 1 })
      .lean()
      .exec((err, schedule) => {
        if (err) {
          return next(err);
        }

        if (!schedule) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Lịch trực đột xuất không tồn tại hoặc đã bị vô hiệu hóa'
            }
          });
        }

        // Validate thời gian ca trực phải nằm trong khoảng thời gian của schedule
        if (startTime < schedule.startTime || endTime > schedule.endTime) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Thời gian ca trực phải nằm trong khoảng thời gian của lịch trực đột xuất'
            }
          });
        }

        // Validate startTime phải nhỏ hơn endTime của schedule
        if (startTime >= schedule.endTime || endTime <= schedule.startTime) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Thời gian ca trực không hợp lệ với lịch trực đột xuất'
            }
          });
        }

        next();
      });
  };

  const checkScheduleConflict = (next) => {
    // Nếu không có dutySpecializedSchedule thì bỏ qua validation này
    if (!dutySpecializedSchedule) {
      return next();
    }

    // Tìm các ca trực có cùng dutySpecializedSchedule và status = 1 (active)
    DutyShiftModel.find({
      dutySpecializedSchedule: dutySpecializedSchedule,
      status: 1,
      officer: officer,
      $or: [
        // Ca trực mới bắt đầu trong khoảng thời gian của ca trực đã có
        {
          startTime: { $lte: startTime },
          endTime: { $gt: startTime }
        },
        // Ca trực mới kết thúc trong khoảng thời gian của ca trực đã có
        {
          startTime: { $lt: endTime },
          endTime: { $gte: endTime }
        },
        // Ca trực mới bao trùm hoàn toàn ca trực đã có
        {
          startTime: { $gte: startTime },
          endTime: { $lte: endTime }
        },
        // Ca trực đã có bao trùm hoàn toàn ca trực mới
        {
          startTime: { $lte: startTime },
          endTime: { $gte: endTime }
        }
      ]
    })
    .lean()
    .exec((err, conflictShifts) => {
      if (err) {
        return next(err);
      }

      if (conflictShifts && conflictShifts.length > 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Cán bộ đã có ca trực khác trong cùng lịch trực với khung giờ trùng lặp'
          }
        });
      }

      next();
    });
  };

  const checkCriminalScheduleConflict = (next) => {
    // Nếu không có dutyCriminalSchedule thì bỏ qua validation này
    if (!dutyCriminalSchedule) {
      return next();
    }

    // Tìm các ca trực có cùng dutyCriminalSchedule và status = 1 (active)
    DutyShiftModel.find({
      dutyCriminalSchedule: dutyCriminalSchedule,
      status: 1,
      officer: officer,
      $or: [
        // Ca trực mới bắt đầu trong khoảng thời gian của ca trực đã có
        {
          startTime: { $lte: startTime },
          endTime: { $gt: startTime }
        },
        // Ca trực mới kết thúc trong khoảng thời gian của ca trực đã có
        {
          startTime: { $lt: endTime },
          endTime: { $gte: endTime }
        },
        // Ca trực mới bao trùm hoàn toàn ca trực đã có
        {
          startTime: { $gte: startTime },
          endTime: { $lte: endTime }
        },
        // Ca trực đã có bao trùm hoàn toàn ca trực mới
        {
          startTime: { $lte: startTime },
          endTime: { $gte: endTime }
        }
      ]
    })
    .lean()
    .exec((err, conflictShifts) => {
      if (err) {
        return next(err);
      }

      if (conflictShifts && conflictShifts.length > 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Cán bộ đã có ca trực khác trong cùng lịch trực với khung giờ trùng lặp'
          }
        });
      }

      next();
    });
  };

  const checkMainScheduleConflict = (next) => {
    // Nếu không có dutyMainSchedule thì bỏ qua validation này
    if (!dutyMainSchedule) {
      return next();
    }

    // Tìm các ca trực có cùng dutyMainSchedule và status = 1 (active)
    DutyShiftModel.find({
      dutyMainSchedule: dutyMainSchedule,
      status: 1,
      officer: officer,
      $or: [
        // Ca trực mới bắt đầu trong khoảng thời gian của ca trực đã có
        {
          startTime: { $lte: startTime },
          endTime: { $gt: startTime }
        },
        // Ca trực mới kết thúc trong khoảng thời gian của ca trực đã có
        {
          startTime: { $lt: endTime },
          endTime: { $gte: endTime }
        },
        // Ca trực mới bao trùm hoàn toàn ca trực đã có
        {
          startTime: { $gte: startTime },
          endTime: { $lte: endTime }
        },
        // Ca trực đã có bao trùm hoàn toàn ca trực mới
        {
          startTime: { $lte: startTime },
          endTime: { $gte: endTime }
        }
      ]
    })
    .lean()
    .exec((err, conflictShifts) => {
      if (err) {
        return next(err);
      }

      if (conflictShifts && conflictShifts.length > 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Cán bộ đã có ca trực khác trong cùng lịch trực với khung giờ trùng lặp'
          }
        });
      }

      next();
    });
  };

  const checkSubScheduleConflict = (next) => {
    // Nếu không có dutySubSchedule thì bỏ qua validation này
    if (!dutySubSchedule) {
      return next();
    }

    // Tìm các ca trực có cùng dutySubSchedule và status = 1 (active)
    DutyShiftModel.find({
      dutySubSchedule: dutySubSchedule,
      status: 1,
      officer: officer,
      $or: [
        // Ca trực mới bắt đầu trong khoảng thời gian của ca trực đã có
        {
          startTime: { $lte: startTime },
          endTime: { $gt: startTime }
        },
        // Ca trực mới kết thúc trong khoảng thời gian của ca trực đã có
        {
          startTime: { $lt: endTime },
          endTime: { $gte: endTime }
        },
        // Ca trực mới bao trùm hoàn toàn ca trực đã có
        {
          startTime: { $gte: startTime },
          endTime: { $lte: endTime }
        },
        // Ca trực đã có bao trùm hoàn toàn ca trực mới
        {
          startTime: { $lte: startTime },
          endTime: { $gte: endTime }
        }
      ]
    })
    .lean()
    .exec((err, conflictShifts) => {
      if (err) {
        return next(err);
      }

      if (conflictShifts && conflictShifts.length > 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Cán bộ đã có ca trực khác trong cùng lịch trực với khung giờ trùng lặp'
          }
        });
      }

      next();
    });
  };

  const checkLocationScheduleConflict = (next) => {
    // Nếu không có dutyLocationSchedule thì bỏ qua validation này
    if (!dutyLocationSchedule) {
      return next();
    }

    // Tìm các ca trực có cùng dutyLocationSchedule và status = 1 (active)
    DutyShiftModel.find({
      dutyLocationSchedule: dutyLocationSchedule,
      status: 1,
      officer: officer,
      $or: [
        // Ca trực mới bắt đầu trong khoảng thời gian của ca trực đã có
        {
          startTime: { $lte: startTime },
          endTime: { $gt: startTime }
        },
        // Ca trực mới kết thúc trong khoảng thời gian của ca trực đã có
        {
          startTime: { $lt: endTime },
          endTime: { $gte: endTime }
        },
        // Ca trực mới bao trùm hoàn toàn ca trực đã có
        {
          startTime: { $gte: startTime },
          endTime: { $lte: endTime }
        },
        // Ca trực đã có bao trùm hoàn toàn ca trực mới
        {
          startTime: { $lte: startTime },
          endTime: { $gte: endTime }
        }
      ]
    })
    .lean()
    .exec((err, conflictShifts) => {
      if (err) {
        return next(err);
      }

      if (conflictShifts && conflictShifts.length > 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Cán bộ đã có ca trực khác trong cùng lịch trực với khung giờ trùng lặp'
          }
        });
      }

      next();
    });
  };

  const checkPatrolScheduleConflict = (next) => {
    // Nếu không có dutyPatrolSchedule thì bỏ qua validation này
    if (!dutyPatrolSchedule) {
      return next();
    }

    // Tìm các ca trực có cùng dutyPatrolSchedule và status = 1 (active)
    DutyShiftModel.find({
      dutyPatrolSchedule: dutyPatrolSchedule,
      status: 1,
      officer: officer,
      $or: [
        // Ca trực mới bắt đầu trong khoảng thời gian của ca trực đã có
        {
          startTime: { $lte: startTime },
          endTime: { $gt: startTime }
        },
        // Ca trực mới kết thúc trong khoảng thời gian của ca trực đã có
        {
          startTime: { $lt: endTime },
          endTime: { $gte: endTime }
        },
        // Ca trực mới bao trùm hoàn toàn ca trực đã có
        {
          startTime: { $gte: startTime },
          endTime: { $lte: endTime }
        },
        // Ca trực đã có bao trùm hoàn toàn ca trực mới
        {
          startTime: { $lte: startTime },
          endTime: { $gte: endTime }
        }
      ]
    })
    .lean()
    .exec((err, conflictShifts) => {
      if (err) {
        return next(err);
      }

      if (conflictShifts && conflictShifts.length > 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Cán bộ đã có ca trực khác trong cùng lịch tuần tra với khung giờ trùng lặp'
          }
        });
      }

      next();
    });
  };

  const checkStadiumScheduleConflict = (next) => {
    // Nếu không có dutyStadiumSchedule thì bỏ qua validation này
    if (!dutyStadiumSchedule) {
      return next();
    }

    // Tìm các ca trực có cùng dutyStadiumSchedule và status = 1 (active)
    DutyShiftModel.find({
      dutyStadiumSchedule: dutyStadiumSchedule,
      status: 1,
      officer: officer,
      $or: [
        // Ca trực mới bắt đầu trong khoảng thời gian của ca trực đã có
        {
          startTime: { $lte: startTime },
          endTime: { $gt: startTime }
        },
        // Ca trực mới kết thúc trong khoảng thời gian của ca trực đã có
        {
          startTime: { $lt: endTime },
          endTime: { $gte: endTime }
        },
        // Ca trực mới bao trùm hoàn toàn ca trực đã có
        {
          startTime: { $gte: startTime },
          endTime: { $lte: endTime }
        },
        // Ca trực đã có bao trùm hoàn toàn ca trực mới
        {
          startTime: { $lte: startTime },
          endTime: { $gte: endTime }
        }
      ]
    })
    .lean()
    .exec((err, conflictShifts) => {
      if (err) {
        return next(err);
      }

      if (conflictShifts && conflictShifts.length > 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Cán bộ đã có ca trực khác trong cùng lịch trực với khung giờ trùng lặp'
          }
        });
      }

      next();
    });
  };

  const checkEmergencyScheduleConflict = (next) => {
    // Nếu không có dutyEmergencySchedule thì bỏ qua validation này
    if (!dutyEmergencySchedule) {
      return next();
    }

    // Tìm các ca trực có cùng dutyEmergencySchedule và status = 1 (active)
    DutyShiftModel.find({
      dutyEmergencySchedule: dutyEmergencySchedule,
      status: 1,
      officer: officer,
      $or: [
        // Ca trực mới bắt đầu trong khoảng thời gian của ca trực đã có
        {
          startTime: { $lte: startTime },
          endTime: { $gt: startTime }
        },
        // Ca trực mới kết thúc trong khoảng thời gian của ca trực đã có
        {
          startTime: { $lt: endTime },
          endTime: { $gte: endTime }
        },
        // Ca trực mới bao trùm hoàn toàn ca trực đã có
        {
          startTime: { $gte: startTime },
          endTime: { $lte: endTime }
        },
        // Ca trực đã có bao trùm hoàn toàn ca trực mới
        {
          startTime: { $lte: startTime },
          endTime: { $gte: endTime }
        }
      ]
    })
    .lean()
    .exec((err, conflictShifts) => {
      if (err) {
        return next(err);
      }

      if (conflictShifts && conflictShifts.length > 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Cán bộ đã có ca trực khác trong cùng lịch trực đột xuất với khung giờ trùng lặp'
          }
        });
      }

      next();
    });
  };

  const createDutyShift = (next) => {
    const shiftData = {
      startTime,
      endTime,
      officer,
      name,
      assignedBy: userId,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    // Thêm các trường tùy chọn nếu có
    if (forLeader) shiftData.forLeader = forLeader;
    if (locationDuty) shiftData.locationDuty = locationDuty;
    if (unit) shiftData.unit = unit;
    if (description) shiftData.description = description;
    if (notes) shiftData.notes = notes;
    if (hasEquipment) shiftData.hasEquipment = hasEquipment;
    if (dutySpecializedSchedule) {
      shiftData.dutySpecializedSchedule = dutySpecializedSchedule;
      shiftData.source = 'specialized'; // Nguồn gốc ca trực từ lịch trực
    }
    if (dutyCriminalSchedule) {
      shiftData.dutyCriminalSchedule = dutyCriminalSchedule;
      shiftData.source = 'criminal'; // Nguồn gốc ca trực từ lịch trực
    }
    if (dutyMainSchedule) {
      shiftData.dutyMainSchedule = dutyMainSchedule;
      shiftData.source = 'main'; // Nguồn gốc ca trực từ lịch trực
    }
    if (dutySubSchedule) {
      shiftData.dutySubSchedule = dutySubSchedule;
      shiftData.source = 'sub'; // Nguồn gốc ca trực từ lịch trực
    }
    if (dutyLocationSchedule) {
      shiftData.dutyLocationSchedule = dutyLocationSchedule;
      shiftData.source = 'location'; // Nguồn gốc ca trực từ lịch trực
    }
    if (dutyPatrolSchedule) {
      shiftData.dutyPatrolSchedule = dutyPatrolSchedule;
      shiftData.source = 'patrol'; // Nguồn gốc ca trực từ lịch tuần tra
    }
    if (dutyStadiumSchedule) {
      shiftData.dutyStadiumSchedule = dutyStadiumSchedule;
      shiftData.source = 'stadium'; // Nguồn gốc ca trực từ lịch trực sân vận động
    }
    if (dutyEmergencySchedule) {
      shiftData.dutyEmergencySchedule = dutyEmergencySchedule;
      shiftData.source = 'emergency'; // Nguồn gốc ca trực từ lịch trực đột xuất
    }
    const newShift = new DutyShiftModel(shiftData);

    newShift.save((err, shift) => {
      if (err) {
        return next(err);
      }

      // Populate thông tin officer và unit để trả về
      DutyShiftModel.findById(shift._id)
        .populate('officer', 'name avatar')
        .populate('unit', 'name')
        .populate('assignedBy', 'name')
        .lean()
        .exec((err, populatedShift) => {
          if (err) {
            return next(err);
          }

          // Trigger statistics update
          StatisticsTrigger.triggerDutyShiftUpdate('create', {
            _id: populatedShift._id,
            officer: populatedShift.officer._id,
            startTime: populatedShift.startTime,
            endTime: populatedShift.endTime,
            status: populatedShift.status,
            source: populatedShift.source
          });

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: {
              head: 'Thông báo',
              body: 'Tạo ca trực thành công'
            },
            data: populatedShift
          });
        });
    });
  };

  async.waterfall([
    checkParams,
    checkOfficerExists,
    checkDutySpecializedSchedule,
    checkDutyCriminalSchedule,
    checkDutyMainSchedule,
    checkDutySubSchedule,
    checkDutyLocationSchedule,
    checkDutyPatrolSchedule,
    checkDutyStadiumSchedule,
    checkDutyEmergencySchedule,
    checkScheduleConflict,
    checkCriminalScheduleConflict,
    checkMainScheduleConflict,
    checkSubScheduleConflict,
    checkLocationScheduleConflict,
    checkPatrolScheduleConflict,
    checkStadiumScheduleConflict,
    checkEmergencyScheduleConflict,
    createDutyShift
  ], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
