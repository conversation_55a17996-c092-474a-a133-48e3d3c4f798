const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const { change_alias } = require('../../../../util/tool');

const JobTypeModel = require('../../../../models/jobType');

module.exports = (req, res) => {
  let queryData;
  let jobTypes;
  let totalCount;

  const checkParams = (next) => {
    const {
      unitId,
      search,
      page = 1,
      limit = 50,
      status
    } = req.body;

    // Kiểm tra page và limit
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);

    if (pageNum < 1 || limitNum < 1 || limitNum > 100) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    queryData = {
      unitId,
      search: search ? search.trim() : '',
      page: pageNum,
      limit: limitNum,
      skip: (pageNum - 1) * limitNum,
      status
    };

    next();
  };

  const buildQuery = (next) => {
    const query = {
      // status: 1 // Chỉ lấy JobType đang hoạt động
    };

    // Lọc theo đơn vị nếu có
    if (queryData.unitId) {
      query.unit = queryData.unitId;
    }

    // Lọc theo trạng thái nếu có
    if ([0, 1].includes(queryData.status)) {
      query.status = queryData.status;
    }

    // Tìm kiếm theo tên nếu có
    if (queryData.search) {
      query.$and = query.$and || [];
      query.$and.push({
        $or: [
          { nameAlias: { $regex: change_alias(queryData.search), $options: 'i' } },
          { description: { $regex: queryData.search, $options: 'i' } }
        ]
      });
    }

    queryData.mongoQuery = query;
    next();
  };

  const getJobTypes = (next) => {
    // Đếm tổng số loại công việc
    JobTypeModel.countDocuments(queryData.mongoQuery, (err, count) => {
      if (err) {
        logger.logError('Lỗi khi đếm loại công việc:', err);
        return next(err);
      }

      totalCount = count;

      // Lấy danh sách loại công việc
      JobTypeModel.find(queryData.mongoQuery)
        .populate('unit', 'name')
        .sort({ name: 1 })
        .skip(queryData.skip)
        .limit(queryData.limit)
        .exec((err, jobTypeList) => {
          if (err) {
            logger.logError('Lỗi khi lấy danh sách loại công việc:', err);
            return next(err);
          }

          jobTypes = jobTypeList;
          next();
        });
    });
  };

  const formatResponse = (next) => {
    const pagination = {
      currentPage: queryData.page,
      totalPages: Math.ceil(totalCount / queryData.limit),
      totalItems: totalCount,
      itemsPerPage: queryData.limit,
      hasNextPage: queryData.page < Math.ceil(totalCount / queryData.limit),
      hasPrevPage: queryData.page > 1
    };

    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      // message: {
      //   head: 'Thông báo',
      //   body: 'Lấy danh sách loại công việc thành công'
      // },
      data: {
        jobTypes: jobTypes,
        pagination
      }
    });
  };

  // Thực thi các bước
  async.waterfall([
    checkParams,
    buildQuery,
    getJobTypes,
    formatResponse
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR,
    });

    res.json(data || err);
  });
};
