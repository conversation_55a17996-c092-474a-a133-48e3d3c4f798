const _ = require('lodash')
const async = require('async')
const Joi = require('joi')
Joi.objectId = require('joi-objectid')(Joi)
const Report = require('../../../models/report')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')

module.exports = (req, res) => {
  const {
    jobType,
    status,
    page = 1,
    limit = 20,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.body
  const userId = req.user.id;

  const validateParams = (next) => {
    const schema = Joi.object({
      jobType: Joi.objectId(),
      status: Joi.string().valid('draft', 'submitted', 'approved', 'rejected'),
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(20),
      sortBy: Joi.string().valid('createdAt', 'title').default('createdAt'),
      sortOrder: Joi.string().valid('asc', 'desc').default('desc')
    })

    const { error } = schema.validate(req.body, { allowUnknown: true })
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next()
  }

  const getReports = (next) => {
    let query = { createdBy: userId, deletedAt: { $exists: false } }

    if (status) query.status = status
    if (jobType) query.jobType = jobType

    const skip = (page - 1) * limit
    const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 }

    async.parallel({
      reports: (cb) => {
        Report
          .find(query)
          .populate('jobType', 'name description')
          .populate('createdBy', 'name')
          .populate('unit', 'name')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean()
          .exec(cb)
      },
      total: (cb) => {
        Report.countDocuments(query, cb)
      }
    }, (err, results) => {
      if (err) {
        return next(err)
      }

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: {
          reports: results.reports,
          pagination: {
            page,
            limit,
            total: results.total,
            pages: Math.ceil(results.total / limit)
          }
        }
      })
    })
  }

  async.waterfall([
    validateParams,
    getReports
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body)
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  })
}