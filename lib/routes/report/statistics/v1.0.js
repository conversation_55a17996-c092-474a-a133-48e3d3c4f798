const _ = require('lodash')
const async = require('async')
const Joi = require('joi')
Joi.objectId = require('joi-objectid')(Joi)
const Report = require('../../../models/report')
const User = require('../../../models/user')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')

module.exports = (req, res) => {
  const {
    type = 'time',
    jobType,
    groupBy = 'day'
  } = req.body
  const userId = req.user.id;

  const validateParams = (next) => {
    const schema = Joi.object({
      type: Joi.string().valid('time', 'location', 'dashboard').default('time'),
      jobType: Joi.objectId(),
      groupBy: Joi.string().valid('hour', 'day', 'week', 'month', 'year').default('day')
    })

    const { error } = schema.validate(req.body, { allowUnknown: true })
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: error.details[0].message
        }
      })
    }

    next()
  }

  const getStatistics = (next) => {
    const mongoose = require('mongoose')

    let query = {
      createdBy: mongoose.Types.ObjectId(userId),
      deletedAt: { $exists: false }
    }

    if (jobType) query.jobType = mongoose.Types.ObjectId(jobType)

    switch (type) {
      case 'time':
        getTimeStatistics(query, groupBy, next)
        break

      case 'location':
        getLocationStatistics(query, next)
        break

      case 'dashboard':
        getDashboardStatistics(query, next)
        break

      default:
        next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Loại thống kê không hợp lệ'
          }
        })
    }
  }

  const formatResponse = (statistics, next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        type,
        filters: {
          jobType,
          groupBy
        },
        statistics
      }
    })
  }

  async.waterfall([
    validateParams,
    getStatistics,
    formatResponse
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body)
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  })
}

// Thống kê theo thời gian - tính tổng từng metric riêng biệt
function getTimeStatistics(query, groupBy, callback) {

  // Tạo group format dựa trên groupBy
  let dateFormat
  switch (groupBy) {
    case 'hour':
      dateFormat = '%Y-%m-%d-%H'
      break
    case 'day':
      dateFormat = '%Y-%m-%d'
      break
    case 'week':
      dateFormat = '%Y-%U'
      break
    case 'month':
      dateFormat = '%Y-%m'
      break
    case 'year':
      dateFormat = '%Y'
      break
    default:
      dateFormat = '%Y-%m-%d'
  }

  // Lấy raw data trước, sau đó xử lý bằng JavaScript
  const pipeline = [
    { $match: query },
    {
      $lookup: {
        from: 'jobtypes',
        localField: 'jobType',
        foreignField: '_id',
        as: 'jobTypeInfo'
      }
    },
    {
      $addFields: {
        createdDate: { $toDate: '$createdAt' },
        jobTypeName: { $arrayElemAt: ['$jobTypeInfo.name', 0] }
      }
    },
    {
      $project: {
        date: { $dateToString: { format: dateFormat, date: '$createdDate' } },
        jobType: '$jobType',
        jobTypeName: '$jobTypeName',
        metrics: '$metrics',
        detailsSize: { $size: { $ifNull: ['$details', []] } }
      }
    },
    { $sort: { date: 1 } }
  ]

  Report.aggregate(pipeline, (err, results) => {
    if (err) {
      return callback(err)
    }

    // Xử lý dữ liệu bằng JavaScript
    const grouped = {}

    results.forEach(item => {
      const key = `${item.date}_${item.jobType}`

      if (!grouped[key]) {
        grouped[key] = {
          date: item.date,
          jobType: item.jobType,
          jobTypeName: item.jobTypeName,
          reportCount: 0,
      metrics: {},
      detailCount: 0
        }
      }

      grouped[key].reportCount++

    // Count details
    grouped[key].detailCount += item.detailsSize || 0

      // Tính tổng từng metric
      Object.keys(item.metrics || {}).forEach(metricKey => {
        const value = item.metrics[metricKey]
        if (typeof value === 'number') {
          if (!grouped[key].metrics[metricKey]) {
            grouped[key].metrics[metricKey] = 0
          }
          grouped[key].metrics[metricKey] += value
        }
      })
    })

    // Format kết quả theo ngày
    const formatted = {}

    Object.values(grouped).forEach(item => {
      const date = item.date

      if (!formatted[date]) {
        formatted[date] = { date, total: 0, byJobType: {} }
      }

      formatted[date].byJobType[item.jobType] = {
        metrics: item.metrics,
        reportCount: item.reportCount,
        detailCount: item.detailCount,
        name: item.jobTypeName
      }

      // Tính total từ tất cả metrics
      const metricsTotal = Object.values(item.metrics)
        .reduce((sum, v) => sum + v, 0)
      formatted[date].total += metricsTotal
    })

    const finalResult = Object.values(formatted).sort((a, b) => a.date.localeCompare(b.date))
    callback(null, finalResult)
  })
}

// Thống kê theo địa điểm - từ details array
function getLocationStatistics(query, callback) {
  const pipeline = [
    { $match: query },
    { $unwind: '$details' },
    {
      $match: {
        'details.location.coordinates': { $exists: true }
      }
    },
    {
      $lookup: {
        from: 'jobtypes',
        localField: 'jobType',
        foreignField: '_id',
        as: 'jobTypeInfo'
      }
    },
    {
      $group: {
        _id: {
          coordinates: '$details.location.coordinates',
          address: '$details.location.address'
        },
        count: { $sum: 1 },
        jobTypes: {
          $addToSet: {
            _id: '$jobType',
            name: { $arrayElemAt: ['$jobTypeInfo.name', 0] }
          }
        },
        latestReport: { $max: '$createdAt' }
      }
    },
    { $sort: { count: -1 } }
  ]

  Report.aggregate(pipeline, (err, results) => {
    if (err) {
      return callback(err)
    }

    const heatmapData = results.map(item => ({
      coordinates: item._id.coordinates,
      address: item._id.address,
      count: item.count,
      jobTypes: item.jobTypes,
      latestReport: item.latestReport,
      intensity: calculateHeatmapIntensity(item.count)
    }))

    callback(null, heatmapData)
  })
}

// Dashboard tổng quan
function getDashboardStatistics(query, callback) {
  // Lấy raw data và xử lý bằng JavaScript
  Report.find(query)
    .populate('jobType', 'name')
    .lean()
    .exec((err, reports) => {
      if (err) {
        return callback(err)
      }

      const grouped = {}
      let totalReports = reports.length
      let todayReports = 0

      const todayStart = new Date()
      todayStart.setHours(0, 0, 0, 0)

  reports.forEach(report => {
        const jobTypeId = report.jobType._id.toString()
        const jobTypeName = report.jobType.name

        if (!grouped[jobTypeId]) {
          grouped[jobTypeId] = {
            _id: { jobType: jobTypeId, jobTypeName },
            reportCount: 0,
    metrics: {},
    detailCount: 0
          }
        }

        grouped[jobTypeId].reportCount++
    grouped[jobTypeId].detailCount += Array.isArray(report.details) ? report.details.length : 0

        // Đếm báo cáo hôm nay
        if (report.createdAt >= todayStart.getTime()) {
          todayReports++
        }

        // Tính tổng metrics
        Object.keys(report.metrics || {}).forEach(key => {
          const value = report.metrics[key]
          if (typeof value === 'number') {
            if (!grouped[jobTypeId].metrics[key]) {
              grouped[jobTypeId].metrics[key] = 0
            }
            grouped[jobTypeId].metrics[key] += value
          }
        })
      })

  const reportsByJobType = Object.values(grouped)
        .sort((a, b) => b.reportCount - a.reportCount)

      callback(null, {
        totalReports,
        todayReports,
        reportsByJobType
      })
    })
}

function calculateHeatmapIntensity(count) {
  if (count <= 1) return 0.2
  if (count <= 5) return 0.4
  if (count <= 10) return 0.6
  if (count <= 20) return 0.8
  return 1.0
}