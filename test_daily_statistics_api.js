/**
 * Test script cho API thống kê điểm danh theo ngày
 * Chạy: node test_daily_statistics_api.js
 */

const axios = require('axios');

// Cấu hình API
const API_BASE_URL = 'http://localhost:9654/api/v1.0';
const TEST_TOKEN = 'your_test_token_here'; // Thay bằng token thực tế

// Test data
const testCases = [
  {
    name: 'Test thống kê 7 ngày gần đây',
    data: {
      startDate: '13-08-2024',
      endDate: '19-08-2024'
    }
  },
  {
    name: 'Test thống kê với filter unit',
    data: {
      startDate: '15-08-2024',
      endDate: '19-08-2024',
      unitId: '60f1b2b3c4d5e6f7a8b9c0d1' // Thay bằng unit ID thực tế
    }
  },
  {
    name: 'Test validation - ngày không hợp lệ',
    data: {
      startDate: '20-08-2024',
      endDate: '19-08-2024' // endDate < startDate
    },
    expectError: true
  },
  {
    name: 'Test validation - khoảng thời gian quá dài',
    data: {
      startDate: '01-01-2024',
      endDate: '28-02-2024' // > 31 ngày
    },
    expectError: true
  }
];

async function testAPI() {
  console.log('🚀 Bắt đầu test API thống kê điểm danh theo ngày...\n');

  for (const testCase of testCases) {
    console.log(`📋 ${testCase.name}`);
    console.log(`   Data: ${JSON.stringify(testCase.data)}`);

    try {
      const response = await axios.post(
        `${API_BASE_URL}/admin/attendance/daily-statistics`,
        testCase.data,
        {
          headers: {
            'Authorization': `Bearer ${TEST_TOKEN}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (testCase.expectError) {
        console.log(`   ❌ Lỗi: Mong đợi lỗi nhưng API trả về thành công`);
      } else {
        console.log(`   ✅ Thành công: ${response.data.message?.head || 'OK'}`);
        
        if (response.data.data) {
          const { period, dailyStatistics } = response.data.data;
          console.log(`   📊 Khoảng thời gian: ${period.startDate} - ${period.endDate}`);
          console.log(`   📈 Số ngày có dữ liệu: ${dailyStatistics.length}`);
          
          // Hiển thị mẫu dữ liệu ngày đầu tiên
          if (dailyStatistics.length > 0) {
            const firstDay = dailyStatistics[0];
            console.log(`   📅 Mẫu dữ liệu ngày ${firstDay.date}:`);
            console.log(`      - Tổng ca: ${firstDay.summary.totalScheduled}`);
            console.log(`      - Đã điểm danh: ${firstDay.summary.totalAttended}`);
            console.log(`      - Đúng giờ: ${firstDay.summary.onTime}`);
            console.log(`      - Đi muộn: ${firstDay.summary.late}`);
            console.log(`      - Vắng mặt: ${firstDay.summary.absent}`);
            console.log(`      - Số đơn vị: ${firstDay.byUnit.length}`);
          }
        }
      }
    } catch (error) {
      if (testCase.expectError) {
        console.log(`   ✅ Lỗi như mong đợi: ${error.response?.data?.message?.body || error.message}`);
      } else {
        console.log(`   ❌ Lỗi không mong đợi: ${error.response?.data?.message?.body || error.message}`);
        if (error.response?.status === 401) {
          console.log(`   🔑 Lưu ý: Cần cập nhật TEST_TOKEN với token hợp lệ`);
        }
      }
    }

    console.log(''); // Dòng trống
  }

  console.log('🏁 Hoàn thành test API');
}

// Chạy test
testAPI().catch(console.error);
